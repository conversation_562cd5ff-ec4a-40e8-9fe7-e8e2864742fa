package ai.yourouter.clickhouse.config;

import com.zaxxer.hikari.HikariDataSource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.sql.DataSource;

/**
 * ClickHouse 配置类
 * 手动声明独立的DataSource + JdbcTemplate，不参与JPA扫描与事务管理
 */
@Slf4j
@EnableAsync
@Configuration
public class ClickHouseConfig {

    /**
     * ClickHouse属性配置类
     */
    @Data
    @ConfigurationProperties(prefix = "ck.datasource")
    public static class ClickHouseProperties {
        private String url;
        private String username;
        private String password;
        private int poolSize = 10;
        private int socketTimeout = 300000;
        private int connectionTimeout = 30000;
    }

    /**
     * ClickHouse属性Bean
     */
    @Bean
    @ConditionalOnProperty(name = "clickhouse.enabled", havingValue = "true", matchIfMissing = true)
    public ClickHouseProperties clickHouseProperties() {
        return new ClickHouseProperties();
    }

    /**
     * ClickHouse数据源
     * 使用HikariCP连接池，高并发下更稳定
     * 明确不设置为Primary，避免与Spring Boot自动配置冲突
     */
    @Bean(name = "ckDataSource")
    @ConditionalOnProperty(name = "clickhouse.enabled", havingValue = "true", matchIfMissing = true)
    public DataSource ckDataSource(ClickHouseProperties props) {
        log.info("初始化ClickHouse数据源 | URL: {} | 用户: {} | 连接池大小: {}", 
                props.getUrl(), props.getUsername(), props.getPoolSize());

        HikariDataSource ds = new HikariDataSource();
        ds.setJdbcUrl(props.getUrl());
        ds.setUsername(props.getUsername());
        ds.setPassword(props.getPassword());
        ds.setDriverClassName("com.clickhouse.jdbc.ClickHouseDriver");
        ds.setMaximumPoolSize(props.getPoolSize());
        
        // ClickHouse特有配置
        ds.addDataSourceProperty("socket_timeout", props.getSocketTimeout());
        ds.addDataSourceProperty("connection_timeout", props.getConnectionTimeout());
        ds.addDataSourceProperty("compress", "true");
        ds.addDataSourceProperty("decompress", "true");
        
        return ds;
    }

    /**
     * ClickHouse JdbcTemplate
     * 用于执行SQL操作
     */
    @Bean(name = "ckJdbcTemplate")
    @ConditionalOnProperty(name = "clickhouse.enabled", havingValue = "true", matchIfMissing = true)
    public JdbcTemplate ckJdbcTemplate(@Qualifier("ckDataSource") DataSource ckDataSource) {
        return new JdbcTemplate(ckDataSource);
    }
}
