package ai.yourouter.clickhouse.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 请求记录实体
 * 用于存储到ClickHouse的请求详细信息
 */
@Data
@Builder
public class RequestRecord {

    /**
     * 请求ID（雪花ID）
     */
    private String requestId;

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 请求时间
     */
    private LocalDateTime requestTime;

    /**
     * 响应时间
     */
    private LocalDateTime responseTime;

    /**
     * 请求耗时（毫秒）
     */
    private Long duration;

    /**
     * 请求路径
     */
    private String requestPath;

    /**
     * HTTP方法
     */
    private String httpMethod;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 真实模型名称
     */
    private String realModelName;

    /**
     * 供应商
     */
    private String vendor;

    /**
     * 密钥ID
     */
    private String keyId;

    /**
     * 密钥名称
     */
    private String keyName;

    /**
     * 是否流式请求
     */
    private Boolean isStream;

    /**
     * 请求体（JSON字符串）
     */
    private String requestBody;

    /**
     * 响应体（JSON字符串）
     */
    private String responseBody;

    /**
     * 请求头（JSON字符串）
     */
    private String requestHeaders;

    /**
     * 响应状态码
     */
    private Integer responseStatus;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * User-Agent
     */
    private String userAgent;

    /**
     * CloudFlare连接IP
     */
    private String cfConnectingIp;

    /**
     * CloudFlare IP国家
     */
    private String cfIpCountry;

    /**
     * CloudFlare Ray ID
     */
    private String cfRay;

    /**
     * 重试次数
     */
    private Long retryCount;

    /**
     * 是否成功
     */
    private Boolean isSuccess;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
