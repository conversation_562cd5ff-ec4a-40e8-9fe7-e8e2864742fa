package ai.yourouter.clickhouse.service;

import ai.yourouter.clickhouse.model.RequestRecord;
import reactor.core.publisher.Mono;

/**
 * ClickHouse服务接口
 * 提供请求记录的存储和查询功能
 */
public interface ClickHouseService {

    /**
     * 异步保存请求记录
     * 
     * @param requestRecord 请求记录
     * @return 保存结果
     */
    Mono<Void> saveRequestRecordAsync(RequestRecord requestRecord);

    /**
     * 同步保存请求记录
     * 
     * @param requestRecord 请求记录
     */
    void saveRequestRecord(RequestRecord requestRecord);

    /**
     * 初始化数据库表
     * 创建请求记录表（如果不存在）
     */
    void initializeDatabase();

    /**
     * 检查ClickHouse连接状态
     * 
     * @return 连接是否正常
     */
    boolean checkConnection();
}
