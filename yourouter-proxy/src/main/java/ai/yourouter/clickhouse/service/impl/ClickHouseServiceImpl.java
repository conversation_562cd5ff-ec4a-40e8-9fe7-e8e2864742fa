package ai.yourouter.clickhouse.service.impl;

import ai.yourouter.clickhouse.model.RequestRecord;
import ai.yourouter.clickhouse.service.ClickHouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.sql.Timestamp;
import java.util.Optional;

/**
 * ClickHouse服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnBean(name = "ckJdbcTemplate")
public class ClickHouseServiceImpl implements ClickHouseService {

    @Qualifier("ckJdbcTemplate")
    private final JdbcTemplate ckJdbcTemplate;

    private static final String CREATE_TABLE_SQL = """
            CREATE TABLE IF NOT EXISTS request_records (
                request_id String,
                organization_id UInt64,
                request_time DateTime64(3),
                response_time DateTime64(3),
                duration UInt64,
                request_path String,
                http_method String,
                model_name String,
                real_model_name String,
                vendor String,
                key_id String,
                key_name String,
                is_stream UInt8,
                request_body String,
                response_body String,
                request_headers String,
                response_status UInt16,
                error_message String,
                client_ip String,
                user_agent String,
                cf_connecting_ip String,
                cf_ip_country String,
                cf_ray String,
                retry_count UInt32,
                is_success UInt8,
                created_at DateTime64(3) DEFAULT now64()
            ) ENGINE = MergeTree()
            ORDER BY (request_time, organization_id)
            PARTITION BY toYYYYMM(request_time)
            SETTINGS index_granularity = 8192
            """;

    private static final String INSERT_SQL = """
            INSERT INTO request_records (
                request_id, organization_id, request_time, response_time, duration,
                request_path, http_method, model_name, real_model_name, vendor, key_id, key_name,
                is_stream, request_body, response_body, request_headers, response_status, error_message,
                client_ip, user_agent, cf_connecting_ip, cf_ip_country, cf_ray, retry_count, is_success
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

    @Override
    public Mono<Void> saveRequestRecordAsync(RequestRecord requestRecord) {
        return Mono.fromRunnable(() -> saveRequestRecord(requestRecord))
                .subscribeOn(Schedulers.boundedElastic())
                .doOnError(error -> log.error("异步保存请求记录失败 | 请求ID: {} | 错误: {}",
                        requestRecord.getRequestId(), error.getMessage()))
                .onErrorResume(error -> {
                    log.warn("异步保存失败，尝试同步保存 | 请求ID: {}", requestRecord.getRequestId());
                    try {
                        saveRequestRecord(requestRecord);
                        return Mono.empty();
                    } catch (Exception e) {
                        log.error("同步保存也失败 | 请求ID: {} | 错误: {}", requestRecord.getRequestId(), e.getMessage());
                        return Mono.empty();
                    }
                })
                .then();
    }

    @Override
    @Async("usageStatisticsExecutor")
    public void saveRequestRecord(RequestRecord requestRecord) {
        try {
            ckJdbcTemplate.update(INSERT_SQL,
                    requestRecord.getRequestId(),
                    requestRecord.getOrganizationId() != null ? requestRecord.getOrganizationId() : 0,
                    requestRecord.getRequestTime() != null ? Timestamp.valueOf(requestRecord.getRequestTime()) : null,
                    requestRecord.getResponseTime() != null ? Timestamp.valueOf(requestRecord.getResponseTime()) : null,
                    requestRecord.getDuration() != null ? requestRecord.getDuration() : 0,
                    requestRecord.getRequestPath(),
                    requestRecord.getHttpMethod(),
                    requestRecord.getModelName(),
                    requestRecord.getRealModelName(),
                    Optional.ofNullable(requestRecord.getVendor()).orElse(""),
                    requestRecord.getKeyId(),
                    requestRecord.getKeyName(),
                    requestRecord.getIsStream() != null && requestRecord.getIsStream() ? 1 : 0,
                    requestRecord.getRequestBody(),
                    requestRecord.getResponseBody(),
                    requestRecord.getRequestHeaders(),
                    requestRecord.getResponseStatus() != null ? requestRecord.getResponseStatus() : 0,
                    Optional.ofNullable(requestRecord.getErrorMessage()).orElse(""),
                    Optional.ofNullable(requestRecord.getClientIp()).orElse(""),
                    Optional.ofNullable(requestRecord.getUserAgent()).orElse(""),
                    Optional.ofNullable(requestRecord.getCfConnectingIp()).orElse(""),
                    Optional.ofNullable(requestRecord.getCfIpCountry()).orElse(""),
                    Optional.ofNullable(requestRecord.getCfRay()).orElse(""),
                    requestRecord.getRetryCount() != null ? requestRecord.getRetryCount() : 0,
                    requestRecord.getIsSuccess() != null && requestRecord.getIsSuccess() ? 1 : 0
            );

            log.debug("成功保存请求记录到ClickHouse | 请求ID: {} | 模型: {} | 耗时: {}ms",
                    requestRecord.getRequestId(), requestRecord.getModelName(), requestRecord.getDuration());

        } catch (Exception e) {
            log.error("保存请求记录到ClickHouse失败 | 请求ID: {} | 错误: {}",
                    requestRecord.getRequestId(), e.getMessage(), e);
            throw new RuntimeException("Failed to save request record to ClickHouse", e);
        }
    }

    @Override
    public void initializeDatabase() {
        try {
            log.debug("执行ClickHouse建表SQL: {}", CREATE_TABLE_SQL.substring(0, Math.min(200, CREATE_TABLE_SQL.length())) + "...");

            ckJdbcTemplate.execute(CREATE_TABLE_SQL);

            log.info("ClickHouse数据库表初始化成功");

        } catch (Exception e) {
            log.error("ClickHouse数据库表初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to initialize ClickHouse database", e);
        }
    }

    @Override
    public boolean checkConnection() {
        try {
            log.debug("检查ClickHouse连接");

            Integer result = ckJdbcTemplate.queryForObject("SELECT 1", Integer.class);

            log.debug("ClickHouse连接检查成功，结果: {}", result);
            return result != null && result == 1;

        } catch (Exception e) {
            log.error("ClickHouse连接检查失败: {}", e.getMessage(), e);
            return false;
        }
    }

}
